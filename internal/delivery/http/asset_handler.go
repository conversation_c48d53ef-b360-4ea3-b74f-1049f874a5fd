package http

import (
	"errors"
	"fmt"
	"net/http"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"
	"futures-asset/service/isolation"
	"futures-asset/util"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type AssetHandlerParam struct {
	dig.In

	AssetUseCase usecase.AssetUseCase
	TrialUseCase usecase.TrialUseCase
}

type assetHandler struct {
	assetUseCase usecase.AssetUseCase
	trialUseCase usecase.TrialUseCase
}

func newAssetHandler(param AssetHandlerParam) *assetHandler {
	return &assetHandler{
		assetUseCase: param.AssetUseCase,
		trialUseCase: param.TrialUseCase,
	}
}

func (handler *assetHandler) GetTotalBalance(c *gin.Context) {
	var req payload.ReqTotalBalance
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	code, data := handler.assetUseCase.TotalBalance(c.Request.Context(), req.ToUseCase())
	if code != http.StatusOK {
		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New("GetTotalBalance")))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *assetHandler) AssetDetail(c *gin.Context) {
	var req payload.ReqAssetDetail
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	data, err := handler.assetUseCase.AssetDetail(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeAssetDetailError, errors.New("asset detail")))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

// UserAsset 获取用户合约资产
func (handler *assetHandler) UserAsset(c *gin.Context) {
	var req payload.SwapParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	asset, err := handler.assetUseCase.UserAsset(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(http.StatusBadRequest, err))

		return
	}

	isolatedList := make([]payload.Frozen, 0)
	for code, frozen := range asset.Frozen {
		haveTrial, base, quote, side := util.FrozenInfo2(code)
		isolated := payload.Frozen{
			HaveTrial: haveTrial,
			Symbol:    util.ContractCode(base, quote),
			Side:      side,
			Frozen:    frozen,
		}
		isolatedList = append(isolatedList, isolated)
	}

	reply := payload.SwapReply{
		Balance:    asset.Balance,
		UID:        asset.UID,
		Valuation:  decimal.Zero,
		Available:  asset.Available,
		FrozenList: isolatedList,
	}

	c.JSON(http.StatusOK, response.NewSuccess(reply))
}

// MaxTransfer 获取最大可提币金额
func (handler *assetHandler) MaxTransfer(c *gin.Context) {
	var req payload.ReqAsset
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	code, withdraw, err := isolation.NewAsset().MaxWithdraw(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(withdraw))
}

// AccountMaxTransfer 获取账户最大可提币金额
func (handler *assetHandler) AccountMaxTransfer(c *gin.Context) {
	var req payload.ReqAsset
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	code, withdraw, err := isolation.NewAsset().AccountMaxWithdraw(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(withdraw))
}

// func (handler *assetHandler) IncrAsset(c *gin.Context) {
// 	var req payload.IncrParam
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	// 全仓爆仓中不能转入
// 	checkBurst := repository.BurstLockParam{UID: req.UID, MarginMode: domain.MarginModeCross}
// 	if swapcache.UserBursting(checkBurst) {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))

// 		return
// 	}

// 	code, reply, err := isolation.NewAsset().Increase(&req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(reply))
// }

// func (handler *assetHandler) DecrAsset(c *gin.Context) {
// 	var req payload.IncrParam
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	// 全仓爆仓中不能转出
// 	checkBurst := repository.BurstLockParam{Liquidation: cache.LiquidationInfo{UID: req.UID, MarginMode: domain.MarginModeCross}}
// 	if swapcache.UserBursting(checkBurst) {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))

// 		return
// 	}

// 	code, reply, err := isolation.NewAsset().Decrease(&req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(reply))
// }

// func (handler *assetHandler) InnerTransfer(c *gin.Context) {
// 	var req []payload.InnerTransfer
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	for _, tran := range req {
// 		if len(tran.FromUserId) == 0 || len(tran.ToUserId) == 0 || tran.Amount.IsNegative() {
// 			continue
// 		}
// 		_, err := isolation.NewAsset().InnerTransfer(tran)
// 		if err != nil {
// 			c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 			return
// 		}
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(""))
// }

// func (handler *assetHandler) LockAsset(c *gin.Context) {
// 	var req payload.LockParam
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	if len(req.Base) == 0 || len(req.Quote) == 0 ||
// 		len(req.OrderId) <= 0 || req.Amount.Sign() <= 0 || req.Side <= 0 ||
// 		req.PosSide <= 0 || req.OrderType <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("param err: "+req.UID)))

// 		return
// 	}
// 	if req.MarginMode != int32(domain.MarginModeCross) && req.MarginMode != int32(domain.MarginModeIsolated) {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("margin mode err: "+req.UID)))

// 		return
// 	}

// 	// 如果用户在爆仓中, 不能进行挂单
// 	checkBurst := repository.BurstLockParam{Liquidation: cache.LiquidationInfo{UID: req.UID, MarginMode: domain.MarginMode(req.MarginMode)}}
// 	switch req.MarginMode {
// 	case int32(domain.MarginModeCross):
// 		if swapcache.UserBursting(checkBurst) && req.IsInnerCall != 1 {
// 			c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))

// 			return
// 		}
// 	case int32(domain.MarginModeIsolated):
// 		checkBurst.ContractCode = req.ContractCode()
// 		if req.PositionMode == domain.HoldModeBoth {
// 			checkBurst.Liquidation.PosSide = domain.BothPos
// 		} else {
// 			checkBurst.Liquidation.PosSide = req.PosSide
// 		}

// 		if swapcache.UserBursting(checkBurst) && req.IsInnerCall != 1 {
// 			c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))

// 			return
// 		}
// 	}

// 	operator, err := isolation.GetOperater(&req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code252407, errors.New("get user asset error")))

// 		return
// 	}
// 	if operator == nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("param side or offset err")))

// 		return
// 	}

// 	reply, err := operator.Lock()
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(reply.Code, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(reply))
// }

// func (handler *assetHandler) UnLockAsset(c *gin.Context) {
// 	var req payload.LockParam
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	if len(req.UID) == 0 || len(req.Base) == 0 || len(req.Quote) == 0 ||
// 		len(req.OrderId) <= 0 || req.Side <= 0 ||
// 		req.PosSide <= 0 || req.OrderType <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("param err: "+req.UID)))

// 		return
// 	}

// 	operater, err := isolation.GetOperater(&req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code252407, errors.New("get user asset error")))

// 		return
// 	}
// 	if operater == nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("param side or offset err")))

// 		return
// 	}

// 	code, err := operater.UnLock()
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}

// 	reply := payload.LockReply{
// 		OrderId: req.OrderId,
// 	}
// 	c.JSON(http.StatusOK, response.NewSuccess(reply))
// }

// func (handler *assetHandler) RecycleTrialAsset(c *gin.Context) {
// 	var req payload.OperateRecycleTrialAsset
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	checkBurst := repository.BurstLockParam{Liquidation: cache.LiquidationInfo{UID: req.UID}}
// 	if swapcache.UserBursting(checkBurst) {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))

// 		return
// 	}

// 	err := handler.trialUseCase.RecycleTrialAsset(c, req.ToUseCase())
// 	if err != nil {
// 		logrus.WithFields(logrus.Fields{
// 			"err": err,
// 			"req": req,
// 		}).Error("trialUseCase.RecycleTrialAsset")
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.InternalError, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
// }

func (handler *assetHandler) AddTrialAsset(c *gin.Context) {
	// var req payload.TrialBase
	// if err := request.ShouldBindJSON(c, &req); err != nil {
	// 	c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
	// 	return
	// }
	// code := req.ParamCheck()
	// if code != domain.CodeOk {
	// 	c.JSON(http.StatusBadRequest, response.NewError(code, errors.New("ParamCheck err")))
	// 	return
	// }

	// checkBurst := repository.BurstLockParam{Liquidation: cache.LiquidationInfo{UID: req.UID}}
	// if swapcache.UserBursting(checkBurst) {
	// 	c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
	// 	return
	// }

	// code = trialasset.NewTrialAsset().OperateRecycle(&req)
	// if code != domain.CodeOk {
	// 	c.JSON(http.StatusBadRequest, response.NewError(code, errors.New("RecycleTrialAsset err")))
	// 	return
	// }

	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
}

// func (handler *assetHandler) GetBalance(c *gin.Context) {
// 	var req payload.ReqAsset
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}
// 	code := req.ParamCheck()
// 	if code != domain.CodeOk {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New("GetBalance err")))
// 		return
// 	}

// 	var data []payload.Asset
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.UID)
// 	asset, err := userCache.Load()
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code252404, errors.New("query asset err")))
// 		return
// 	}
// 	pCache := price.New()
// 	totalBalance := asset.CBalance(req.Currency)

// 	rate := pCache.SpotURate(req.Currency)
// 	data = append(data, payload.Asset{
// 		Currency:       req.Currency,
// 		TotalTotal:     totalBalance,
// 		BTCValuation:   swapcache.CalcBtcValuation(totalBalance.Mul(rate), pCache),
// 		TotalProfit:    decimal.Decimal{},
// 	})

// 	c.JSON(http.StatusOK, response.NewSuccess(data))
// }

// func (handler *assetHandler) Trade(c *gin.Context) {
// 	var req payload.TradeParam
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}
// 	// 计算实际扣除手续费
// 	if req.Maker.Fee.Sign() < 0 {
// 		req.Maker.DeductFee = decimal.Zero
// 	}
// 	if req.Taker.Fee.Sign() < 0 {
// 		req.Taker.DeductFee = decimal.Zero
// 	}
// 	originParam := req
// 	req.Maker.Fee = req.Maker.Fee.Sub(req.Maker.DeductFee)
// 	req.Taker.Fee = req.Taker.Fee.Sub(req.Taker.DeductFee)
// 	// 参数校验
// 	code, err := checkTradeParam(&req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))
// 		return
// 	}
// 	// 成交
// 	logrus.Info(0, "====== /trade maker", req.Maker.UID, "taker", req.Taker.UID, "param:", fmt.Sprintf("%+v", req), "originParam", fmt.Sprintf("%+v", originParam))
// 	code, reply, err := coupling.NewPos().Trade(&req, originParam)
// 	logrus.Info(0, "====== /trade maker", req.Maker.UID, "taker", req.Taker.UID, "code:", code, "reply", fmt.Sprintf("%+v", reply), "err", err)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))
// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(reply))
// }

// func checkTradeParam(param *payload.TradeParam) (domain.Code, error) {
// 	if param.Taker.Side <= 0 || param.Taker.PosSide <= 0 {
// 		logrus.Errorf("taker side or offset err: side:%+v offset:%+v", param.Taker.Side, param.Taker.PosSide)
// 		return domain.CodeParamInvalid, fmt.Errorf("taker side or offset err: side:%d offset:%d", param.Taker.Side, param.Taker.PosSide)
// 	}
// 	if param.Maker.Side <= 0 || param.Maker.PosSide <= 0 {
// 		logrus.Errorf("maker side or offset err: side:%+v offset:%+v", param.Maker.Side, param.Maker.PosSide)
// 		return domain.CodeParamInvalid, fmt.Errorf("maker side or offset err: side:%d offset:%d", param.Maker.Side, param.Maker.PosSide)
// 	}
// 	if len(param.Taker.UID) <= 0 || len(param.Maker.UID) <= 0 {
// 		logrus.Errorf("taker or maker userid err")
// 		return domain.CodeParamInvalid, fmt.Errorf("taker or maker userid err: %+v", *param)

// 	}
// 	if param.Amount.Sign() <= 0 || len(param.Base) <= 0 || len(param.Quote) <= 0 || len(param.TradeId) <= 0 {
// 		logrus.Errorf("amount or tradeid err")
// 		return domain.CodeParamInvalid, fmt.Errorf("amount or tradeid err: %+v", *param)

// 	}
// 	if param.Taker.Leverage <= 0 || param.Maker.Leverage <= 0 {
// 		logrus.Errorf("leverage err")
// 		return domain.CodeParamInvalid, fmt.Errorf("leverage err: %+v", *param)

// 	}
// 	if param.Taker.MarginMode != int32(domain.MarginModeCross) && param.Taker.MarginMode != int32(domain.MarginModeIsolated) {
// 		logrus.Errorf("taker margin mode err: %+v", *param)
// 		return domain.CodeParamInvalid, fmt.Errorf("taker margin mode err: %+v", *param)

// 	}
// 	if param.Maker.MarginMode != int32(domain.MarginModeCross) && param.Maker.MarginMode != int32(domain.MarginModeIsolated) {
// 		logrus.Errorf("maker margin mode err: %+v", *param)
// 		return domain.CodeParamInvalid, fmt.Errorf("maker margin mode err: %+v", *param)
// 	}

// 	return http.StatusOK, nil
// }

func (handler *assetHandler) GetUserAsset(c *gin.Context) {
	// 目前产品需求 只根据 user id 查 估值类型默认写死，之后可能根据后台传的算
	var req payload.SwapParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	code, data := handler.assetUseCase.GetUserAssetAndPos(c.Request.Context(), req.ToUseCase())
	if code != http.StatusOK {
		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New(domain.ErrMsg[code])))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

// SumUserTotalAsset 统计用户总资产
func (handler *assetHandler) SumUserTotalAsset(c *gin.Context) {
	res, err := handler.assetUseCase.SumUserTotalAsset(c.Request.Context())
	if err != nil {
		logrus.Error(fmt.Sprintf("SumUserTotalAsset err:%v", err))
		c.JSON(http.StatusInternalServerError, response.NewError(http.StatusInternalServerError, errors.New("SumUserTotalAsset err")))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(res))
}
