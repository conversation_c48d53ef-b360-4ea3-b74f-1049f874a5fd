package entity

import (
	"futures-asset/util"

	"github.com/shopspring/decimal"
)

// BillAsset contract asset bill log
type BillAsset struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	UID         string          `gorm:"user_id;type:varchar(20);not null" json:"uid"`                  // 用户ID
	OperateId   string          `gorm:"operate_id;type:varchar(50);not null" json:"operate_id"`        // 操作id 结算的时候是结算的唯一id， 爆仓的时候是爆仓操作的唯一id
	BillId      string          `gorm:"bill_id;type:varchar(50);not null" json:"bill_id"`              // 账单ID
	Symbol      string          `gorm:"symbol;type:varchar(50);" json:"symbol"`                        // 合约代码
	Currency    string          `gorm:"currency;type:varchar(25);not null" json:"currency"`            // 资产币种
	BillType    int             `gorm:"bill_type;" sql:"type:SMALLINT;" json:"bill_type"`              // 账单类型
	Amount      decimal.Decimal `gorm:"amount" sql:"type:decimal(30,15);" json:"amount"`               // 数量
	OperateTime int64           `gorm:"operate_time;not null" json:"operate_time"`                     // 创建时间
	FundRate    decimal.Decimal `gorm:"fund_rate;" sql:"type:decimal(30,15);" json:"fund_rate"`        // 资金费率
	MarkPrice   decimal.Decimal `gorm:"mark_price" sql:"type:decimal(30,15);" json:"mark_price"`       // 标记价格
	FromPair    string          `gorm:"from_pair;type:varchar(20)" json:"from_pair"`                   // 转出币对
	ToPair      string          `gorm:"to_pair;type:varchar(20)" json:"to_pair"`                       // 转入币对
	FromAccount int             `gorm:"from_account;" json:"from_account"`                             // 转出账户
	ToAccount   int             `gorm:"to_account;" json:"to_account"`                                 // 转入账户
	RecycleOpId string          `gorm:"recycle_op_id;type:varchar(20);not null;" json:"recycle_op_id"` // 体验金回收的唯一id
}

// TableName get bill swap table name
func (slf *BillAsset) TableName() string {
	return "bill_swap" + util.DayLayout(slf.OperateTime, util.EnumNanosecond)
}

// RobotTableName get bill swap table name
func (slf *BillAsset) RobotTableName() string {
	return "bill_swap_robot" + util.DayLayout(slf.OperateTime, util.EnumNanosecond)
}
