package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/libs/pager"
	"futures-asset/util"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type FundingRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	RDB *redis.ClusterClient `name:"redis-cluster"`

	FormulaRepository repository.FormulaRepository
	SettingRepo       repository.SettingRepository
}

type fundingRepository struct {
	db  *gorm.DB
	rdb *redis.ClusterClient

	formulaRepo repository.FormulaRepository
	SettingRepo repository.SettingRepository
}

func NewFundingRepository(param FundingRepositoryParam) repository.FundingRepository {
	return &fundingRepository{
		db:  param.DB,
		rdb: param.RDB,

		formulaRepo: param.FormulaRepository,
		SettingRepo: param.SettingRepo,
	}
}

// FundRate 获取资金费率 首先把当前这一分钟的资金费率放到redis中 然后取最近30次的 求平均值
func (repo *fundingRepository) FundRate(ctx context.Context, contractCode string, cfg repository.ContractPair) decimal.Decimal {
	fundRate := repo.formulaRepo.FundingRate(ctx, contractCode, cfg) // 新计算逻辑

	// HY-80 资金费率特殊逻辑处理-根据平台与用户持仓方向最终确认资金费率
	// 每天 0，8，16 点结算
	nowTime := time.Now()
	if nowTime.Hour()%8 == 0 {
		if nowTime.Minute() == 0 {
			usersKey := domain.UserPosBase.Key(contractCode)
			users, err := repo.rdb.HGetAll(ctx, usersKey).Result()
			if err != nil {
				logrus.Error("usersKey HGetAll", usersKey, "error:", err)
				return fundRate
			}
			totalLongPos := decimal.Zero
			totalShortPos := decimal.Zero
			for _, userPos := range users {
				userPosSwap := repository.UserHoldPos{}
				err = json.Unmarshal([]byte(userPos), &userPosSwap)
				if err != nil {
					logrus.Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
					continue
				}
				if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() && userPosSwap.BothPos.IsZero() {
					continue
				}
				// HY-334,HY-80 资金费率排除机器人持仓
				if domain.RobotUsers.HasKey(userPosSwap.UID) {
					continue
				}
				// 单向持仓需要特殊处理
				if !userPosSwap.BothPos.IsZero() && userPosSwap.BothTrialMargin.LessThanOrEqual(decimal.Zero) {
					if userPosSwap.BothPos.IsPositive() {
						totalLongPos = totalLongPos.Add(userPosSwap.BothPos.Abs())
					} else if userPosSwap.BothPos.IsNegative() {
						totalShortPos = totalShortPos.Add(userPosSwap.BothPos.Abs())
					}
				} else {
					// Isolated true 是逐仓 逐仓和全仓处理方式不一样
					if domain.MarginMode(userPosSwap.MarginMode) == domain.MarginModeIsolated {
						if userPosSwap.LongPos.IsPositive() && userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
							totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
						}
						if userPosSwap.ShortPos.IsPositive() && userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
							totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
						}
					} else {
						realPos := decimal.Zero
						longPos := decimal.Zero
						shortPos := decimal.Zero
						if userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
							longPos = userPosSwap.LongPos
						}
						if userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
							shortPos = userPosSwap.ShortPos
						}
						realPos = longPos.Sub(shortPos)
						if realPos.IsPositive() {
							totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
						} else if realPos.IsNegative() {
							totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
						}
					}
				}
			}
			// 获取机器人持仓
			// robotLongPos := decimal.Zero
			// robotShortPos := decimal.Zero
			// base, quote := util.BaseQuote(contractCode)
			// searchParam := &entity.PositionSearch{
			// 	Base:     base,
			// 	Quote:    quote,
			// 	PageSize: 20,
			// 	PageNum:  1,
			// 	PosSwap: entity.Position{
			// 		AccountType: "swap",
			// 		PosStatus:   1,
			// 	},
			// }
			// _, posList, err := swap.GetPosSwapList(searchParam, []int{domain.UserTypePlatformRobot}, []int{}, true)
			// if err != nil {
			// 	fmt.Printf("get pos err: %s\n", err.Error())
			// 	return fundRate
			// }
			// for _, p := range posList {
			// 	if p.PosSide == domain.LongPos {
			// 		robotLongPos = robotLongPos.Add(p.Pos.Abs())
			// 	}
			// 	if p.PosSide == domain.ShortPos {
			// 		robotShortPos = robotShortPos.Add(p.Pos.Abs())
			// 	}
			// 	if p.PosSide == domain.BothPos && p.Pos.IsPositive() {
			// 		robotLongPos = robotLongPos.Add(p.Pos.Abs())
			// 	}
			// 	if p.PosSide == domain.BothPos && p.Pos.IsNegative() {
			// 		robotShortPos = robotShortPos.Add(p.Pos.Abs())
			// 	}
			// }

			logrus.Infof("total pos long: %s pos short: %s contractCode: %s funding rate: %s", totalLongPos.String(), totalShortPos.String(), contractCode, fundRate.String())
			if totalLongPos.GreaterThan(totalShortPos) {
				return fundRate.Abs()
			}
			if totalLongPos.LessThan(totalShortPos) {
				return fundRate.Abs().Neg()
			}
		}
	}

	return fundRate
}

func (repo *fundingRepository) GetFundRateList(ctx context.Context, req *repository.FundRateParam) (repository.FundRateReply, error) {
	reply := repository.FundRateReply{
		Page: pager.Page{
			PageIndex: req.PageIndex,
			PageSize:  req.PageSize,
		},
		List: make([]entity.LogFundingRate, 0),
	}

	total, list, err := repo.getFundRateList(ctx, req)
	if err != nil {
		return reply, errors.Wrap(err, "get fund rate list")
	}
	for i := 0; i < len(list); i++ {
		list[i].FundRate = list[i].FundRate.Mul(decimal.NewFromFloat(100))
	}
	reply.Total = total
	reply.List = list

	return reply, nil
}

func (repo *fundingRepository) getFundRateList(ctx context.Context, req *repository.FundRateParam) (int64, []entity.LogFundingRate, error) {
	// 获取数据和总数
	fundRateList := make([]entity.LogFundingRate, 0)
	var total int64
	if req.PageIndex*req.PageSize == 0 {
		return total, fundRateList, nil
	}
	query := "id > 0"
	args := make([]interface{}, 0)

	if req.Base != "" {
		query += fmt.Sprintf(" and base=?")
		args = append(args, req.Base)
	}
	if req.Quote != "" {
		query += fmt.Sprintf(" and quote=?")
		args = append(args, req.Quote)
	}
	if req.StartTime != 0 {
		query += fmt.Sprintf(" and operate_time>=?")
		args = append(args, req.StartTime)
	}
	if req.EndTime != 0 {
		query += fmt.Sprintf(" and operate_time<=?")
		args = append(args, req.EndTime)
	}

	sql := repo.db.Model(&entity.LogFundingRate{}).Select("*")
	if req.PageIndex != 0 && req.PageSize != 0 {
		sql = sql.Offset((req.PageIndex - 1) * req.PageSize).Limit(req.PageSize)
	}
	if query != "" && args != nil {
		sql = sql.Where(query, args...)
	}
	if fmt.Sprintf("%s %s", "operate_time", "DESC") != "" {
		sql = sql.Order(fmt.Sprintf("%s %s", "operate_time", "DESC"))
	}

	// 获取列表数据
	if err := sql.Find(&fundRateList).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return 0, nil, fmt.Errorf("find funding rates failed: %w", err)
		}
	}
	if err := sql.Offset(-1).Count(&total).Error; err != nil {
		return 0, nil, fmt.Errorf("count funding rates failed: %w", err)
	}

	return total, fundRateList, nil
}

func (repo *fundingRepository) GetFundRate(ctx context.Context, contractCode string) (repository.FundingRate, error) {
	fundMsg := repository.FundingRate{
		Symbol: contractCode,
	}

	contractConfig, err := repo.SettingRepo.GetPairSettingInfo(ctx, contractCode)
	if err != nil {
		return fundMsg, errors.Wrap(err, "get contract config")
	}

	fundMsg.FundRate = repo.FundRate(ctx, contractCode, *contractConfig)
	fundMsg.EndTime = util.GetSettlementTime()
	fundMsg.StartTime = time.Now().Unix()

	return fundMsg, nil
}

func (repo *fundingRepository) FundingFeeList(ctx context.Context, req *repository.FundingFeeListParam) (repository.FundingFeeList, error) {
	r := repository.FundingFeeList{
		Page: pager.Page{
			PageIndex: req.PageIndex,
			PageSize:  req.PageSize,
		},
	}
	funding := &entity.LogFundingFee{}
	sql := repo.db.Table(funding.TableName())
	if req.AccountType != "" {
		sql = sql.Where("account_type = ?", req.AccountType)
	}
	if req.Base != "" {
		sql = sql.Where("base = ?", req.Base)
	}
	if req.Quote != "" {
		sql = sql.Where("quote = ?", req.Quote)
	}
	if req.MarginMode != 0 {
		sql = sql.Where("margin_mode = ?", req.MarginMode)
	}
	if req.PosSide != 0 {
		sql = sql.Where("pos_side = ?", req.PosSide)
	}
	if req.UID != "" {
		sql = sql.Where("user_id = ?", req.UID)
	}
	if len(req.UserType) > 0 {
		sql = sql.Where("user_type in (?)", req.UserType)
	}
	if req.FundingId != "" {
		sql = sql.Where("funding_id = ?", req.FundingId)
	}
	if req.Direction != 0 {
		sql = sql.Where("direction = ?", req.Direction)
	}
	if req.StartTime != 0 {
		sql = sql.Where("operate_time >= ?", req.StartTime*1e9)
	}
	if req.EndTime != 0 {
		sql = sql.Where("operate_time <= ?", req.EndTime*1e9)
	}
	sql.Count(&r.Total)

	err := sql.Offset((req.PageIndex - 1) * req.PageSize).Limit(req.PageSize).Order("operate_time DESC").Find(&r.List).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return r, errors.Wrap(err, "db err")
	}

	return r, nil
}

func (repo *fundingRepository) FundRateAll(ctx context.Context) (r repository.FundRateAll, err error) {
	r.FundList = []repository.FundingRate{}
	var lastFundRate map[string]entity.LogFundingRate

	wg := new(sync.WaitGroup)
	wg.Add(2)
	r.FundList = repo.AllFundData(ctx, wg)
	lastFundRate = repo.LastFundMap(ctx, wg)
	wg.Wait()

	for i := 0; i < len(r.FundList); i++ {
		if _, ok := lastFundRate[r.FundList[i].Symbol]; ok {
			r.FundList[i].LastFundRate = lastFundRate[r.FundList[i].Symbol].FundRate.Mul(decimal.NewFromInt(100))
			r.FundList[i].LastTime = lastFundRate[r.FundList[i].Symbol].OperateTime
		}
		r.FundList[i].FundRate = r.FundList[i].FundRate.Mul(decimal.NewFromInt(100))
	}
	r.EndTime = util.GetSettlementTime()
	r.StartTime = time.Now().Unix()

	return
}

func (repo *fundingRepository) LastFundMap(ctx context.Context, wg *sync.WaitGroup) (r map[string]entity.LogFundingRate) {
	defer wg.Done()
	var fundList []entity.LogFundingRate
	r = make(map[string]entity.LogFundingRate)
	sqlStr := "SELECT * FROM log_fund_rate WHERE operate_time = (SELECT MAX(operate_time) FROM log_fund_rate)"
	repo.db.Raw(sqlStr).Scan(&fundList)
	for i := 0; i < len(fundList); i++ {
		r[util.ContractCode(fundList[i].Base, fundList[i].Quote)] = fundList[i]
	}

	return
}

func (repo *fundingRepository) AllFundData(ctx context.Context, wg *sync.WaitGroup) (r []repository.FundingRate) {
	defer wg.Done()
	r = make([]repository.FundingRate, 0)

	contractSettings, err := repo.SettingRepo.GetAllPairSettingInfo(ctx)
	if err != nil {
		logrus.Error("makeFundRates get all contract config err.")
		return
	}
	for contractCode, contractConfig := range contractSettings {
		if contractConfig.State != domain.ContractStart {
			continue
		}
		fundData := repository.FundingRate{
			Symbol:   contractCode,
			ShowRank: contractConfig.ShowRank,
		}
		fundData.FundRate = repo.FundRate(ctx, contractCode, contractConfig)
		r = append(r, fundData)
	}
	sort.Slice(r, func(i, j int) bool {
		return r[i].ShowRank < r[j].ShowRank
	})

	return r
}

func (repo *fundingRepository) GetBaseNum(ctx context.Context, key string) int {
	baseNumStr, err := repo.rdb.Get(ctx, key).Result()
	if err != nil {
		return domain.DefaultBaseNum
	}

	baseNum, err := strconv.Atoi(baseNumStr)
	if err != nil {
		return domain.DefaultBaseNum
	}

	return baseNum
}
