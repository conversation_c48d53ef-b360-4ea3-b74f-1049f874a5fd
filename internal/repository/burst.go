package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	"gorm.io/gorm"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type BurstRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	RDB *redis.ClusterClient `name:"redis-cluster"`

	SettingRepo repository.SettingRepository
}

type burstRepository struct {
	db  *gorm.DB
	rdb *redis.ClusterClient

	settingRepo repository.SettingRepository
}

func NewBurstRepository(param BurstRepositoryParam) repository.BurstRepository {
	return &burstRepository{
		db:  param.DB,
		rdb: param.RDB,

		settingRepo: param.SettingRepo,
	}
}

// IsBursting implements repository.BurstRepository.
func (repo *burstRepository) IsBursting(ctx context.Context, param repository.CheckBurstParam) (bool, error) {
	// 根据是否为体验金仓位选择对应的Redis key获取函数
	getBurstLockKey := domain.GetBurstLockRedisKey
	if param.IsTrialPos {
		getBurstLockKey = domain.GetTrialBurstLockRedisKey
	}

	// 如果指定了保证金模式, 只检查对应模式的爆仓锁
	switch param.MarginMode {
	case futuresassetpb.MarginMode_MARGIN_MODE_CROSS, futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED:
		val, err := repo.rdb.Exists(ctx, getBurstLockKey(param.UID, param.MarginMode)).Result()
		return val > 0, err
	}

	// 如果未指定保证金模式, 则检查全仓和逐仓的爆仓锁
	exCrossedLock, err := repo.rdb.Exists(ctx, getBurstLockKey(param.UID, futuresassetpb.MarginMode_MARGIN_MODE_CROSS)).Result()
	if err != nil {
		return false, err
	}

	exIsolatedLock, err := repo.rdb.Exists(ctx, getBurstLockKey(param.UID, futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED)).Result()
	if err != nil {
		return false, err
	}

	return exCrossedLock > 0 || exIsolatedLock > 0, nil
}

func (repo *burstRepository) GetBurstInfoByTableNameAndId(ctx context.Context, id string) (*entity.BurstSwap, error) {
	burstInfo := new(entity.BurstSwap)
	err := repo.db.Table(burstInfo.TableName()).Where("id=?", id).First(burstInfo).Error
	if err != nil {
		logrus.Error("burstService GetBurstInfoByTableNameAndId error:", err, id)
		return nil, err
	}

	return burstInfo, nil
}

func (repo *burstRepository) SearchBurstInfos(ctx context.Context, conditions map[string]interface{}, ranges map[string]map[string]interface{}, pageNum, pageSize int) (int64, []entity.BurstSwap) {
	burstInfoList := make([]entity.BurstSwap, 0)
	total := struct {
		Counter int64 `json:"counter"`
	}{}
	model := entity.BurstSwap{}
	session := repo.db.Table(model.TableName())
	session = repo._buildBurstListSearch(session, conditions, ranges)
	session.Select("count(id) as counter").Find(&total)
	session.Order("`burst_time` DESC").Offset((pageNum - 1) * pageSize).
		Limit(pageSize).Find(&burstInfoList)
	return total.Counter, burstInfoList
}

func (repo *burstRepository) _buildBurstListSearch(_db *gorm.DB, conditions map[string]interface{}, ranges map[string]map[string]interface{}) *gorm.DB {
	if ranges != nil {
		if burstTimeRangeMap, ok := ranges["burst_time"]; ok {
			for key, data := range burstTimeRangeMap {
				switch key {
				case "gte":
					_db = _db.Where("`burst_time` >= ?", data)
				case "lte":
					_db = _db.Where("`burst_time` <= ?", data)
				}
			}
		}
	}

	if conditions != nil {
		for key, data := range conditions {
			switch key {
			case "user_type", "liquidation_type":
				if intSlice, intSliceOk := data.([]int); intSliceOk {
					ids := make([]string, 0)
					for _, id := range intSlice {
						ids = append(ids, fmt.Sprintf("%d", id))
					}
					_db = _db.Where(fmt.Sprintf("`%s` IN (%s)", key, strings.Join(ids, ",")))
				}
			case "currency", "base", "quote", "pos_type", "pos_id", "margin_mode", "user_id", "burst_id", "status":
				_db = _db.Where(fmt.Sprintf("`%s` = ?", key), data)
			default:
			}
		}
	}

	return _db
}

func (repo *burstRepository) StatBurstTimesByTableNameList(ctx context.Context, startTime, endTime time.Time) []entity.StatBurstInfo {
	statBurstInfoList := make([]entity.StatBurstInfo, 0)
	userBurstStats := make(map[string]map[string]map[string]string)
	bursts := make([]struct {
		Id  string `json:"id"`
		UID string `json:"user_id"`
	}, 0)
	model := entity.BurstSwap{}
	err := repo.db.Table(model.TableName()).Select("id,user_id").
		Where("liquidation_type=? and level=1 and burst_time >= ? and burst_time < ?", domain.LiquidationTypeBurst, startTime.UnixNano(), endTime.UnixNano()).
		Find(&bursts).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logrus.Error("burstService StatBurstTimesByTableNameList error:", err, domain.LiquidationTypeBurst)
		return statBurstInfoList
	}
	for _, burst := range bursts {
		if _, ok := userBurstStats[burst.UID]; ok {
			userBurstStats[burst.UID]["burst"][burst.Id] = burst.Id
		} else {
			userBurstStats[burst.UID] = map[string]map[string]string{
				"burst": {
					burst.Id: burst.Id,
				},
				"reduce": {},
			}
		}
	}

	reduces := make([]struct {
		Id  string `json:"id"`
		UID string `json:"user_id"`
	}, 0)
	err = repo.db.Table(model.TableName()).Select("id,user_id").
		Where("liquidation_type<>? and level<>1 and burst_time >= ? and burst_time < ?", domain.LiquidationTypeBurst, startTime.UnixNano(), endTime.UnixNano()).
		Find(&reduces).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logrus.Error("burstService StatBurstTimesByTableNameList error:", err)
		return statBurstInfoList
	}
	for _, reduce := range reduces {
		if _, ok := userBurstStats[reduce.UID]; ok {
			userBurstStats[reduce.UID]["reduce"][reduce.Id] = reduce.Id
		} else {
			userBurstStats[reduce.UID] = map[string]map[string]string{
				"burst": {},
				"reduce": {
					reduce.Id: reduce.Id,
				},
			}
		}
	}
	for uid, statMap := range userBurstStats {
		statBurstInfoList = append(statBurstInfoList, entity.StatBurstInfo{
			UID:       uid,
			BurstNum:  len(statMap["burst"]),
			ReduceNum: len(statMap["reduce"]),
		})
	}

	return statBurstInfoList
}

// 移除体验金用户编号中体验金标识
func (repo *burstRepository) RemoveUserIdTrial(userId string) string {
	return strings.TrimSuffix(userId, "-trial")
}

// 用户编号中是否包含体验金标识
func (repo *burstRepository) UserIdIsContainTrial(userId string) bool {
	return strings.Contains(userId, "-trial")
}

// 获取体验金用户标识
func (repo *burstRepository) TrialUserId(uid string) string {
	return fmt.Sprintf("%s-trial", uid)
}

func (repo *burstRepository) RivalScoreRate(ctx context.Context, uid, symbol string, posSide int32, isTrialPos bool) decimal.Decimal {
	rivalRankKey := domain.GetBurstRivalLongRankRedisKey(symbol)
	if posSide == domain.ShortPos {
		rivalRankKey = domain.GetBurstRivalShortRankRedisKey(symbol)
	}
	if isTrialPos {
		uid = repo.TrialUserId(uid)
	}
	total, err := repo.rdb.ZCard(ctx, rivalRankKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		logrus.Errorln(fmt.Sprintf("RivalScoreRate %s ZCard error: %s", rivalRankKey, err))
		return decimal.Zero
	}
	if isTrialPos {
		uid = repo.TrialUserId(uid)
	}
	rank, err := repo.rdb.ZRank(ctx, rivalRankKey, uid).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		logrus.Errorln(fmt.Sprintf("RivalScoreRate %s ZRevRank error: %s", rivalRankKey, err))
		return decimal.Zero
	}

	// 计算排名占比
	if total == 0 {
		return decimal.Zero
	}
	rankPercent := decimal.NewFromFloat(float64(rank) + 1).Div(decimal.NewFromFloat(float64(total))).Truncate(domain.RatePrecision)

	return rankPercent
}

func (repo *burstRepository) GetUserBurstLevel(ctx context.Context, uid, symbol string, marginMode futuresassetpb.MarginMode, posSide futuresassetpb.PosSide) string {
	hashKey := domain.AssetPrefix.Key(uid)
	switch marginMode {
	case futuresassetpb.MarginMode_MARGIN_MODE_CROSS:
		level, err := repo.rdb.HGet(ctx, hashKey, "burst:level").Result()
		if err != nil {
			if err == redis.Nil {
				return "0"
			}
			logrus.Error("SaveUserBurstLevel HSet error:", err, "uid:", uid, "symbol:", symbol, "marginMode:", marginMode)
		}
		return level

	case futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED:
		burstSubKey := fmt.Sprintf("burst:level:%s:%d", strings.ToUpper(symbol), posSide)
		level, err := repo.rdb.HGet(ctx, hashKey, burstSubKey).Result()
		if err != nil {
			if err == redis.Nil {
				return "0"
			}
			logrus.Error("SaveUserBurstLevel HSet error:", err, "uid:", uid, "symbol:", symbol, "marginMode:", marginMode, "posSide:", posSide)
		}
		return level

	default:
	}

	return "0"
}

func (repo *burstRepository) SaveUserBurstLevel(ctx context.Context, uid, symbol, level string, marginMode futuresassetpb.MarginMode, posSide futuresassetpb.PosSide) error {
	hashKey := domain.AssetPrefix.Key(uid)
	switch marginMode {
	case futuresassetpb.MarginMode_MARGIN_MODE_CROSS:
		err := repo.rdb.HSet(ctx, hashKey, "burst:level", level).Err()
		if err != nil {
			logrus.Error("SaveUserBurstLevel HSet error:", err, "uid:", uid, "symbol:", symbol, "marginMode:", marginMode, "level:", level)
		}

	case futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED:
		burstSubKey := fmt.Sprintf("burst:level:%s:%d", strings.ToUpper(symbol), posSide)
		err := repo.rdb.HSet(ctx, hashKey, burstSubKey, level).Err()
		if err != nil {
			logrus.Error("SaveUserBurstLevel HSet error:", err, "uid:", uid, "symbol:", symbol, "marginMode:", marginMode, "posSide:", posSide, "level:", level)
		}

	default:
		return errors.New("margin mode unknown")
	}

	return nil
}

func (repo *burstRepository) RivalScore(ctx context.Context, uid, contractCode string, posSide int32, isTrialPos bool) decimal.Decimal {
	rivalRankKey := domain.GetBurstRivalLongRankRedisKey(contractCode)
	if posSide == domain.ShortPos {
		rivalRankKey = domain.GetBurstRivalShortRankRedisKey(contractCode)
	}
	if isTrialPos {
		uid = repo.TrialUserId(uid)
	}
	score, err := repo.rdb.ZScore(ctx, rivalRankKey, uid).Result()
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("pushRivalScore %s ZCard error: %s", rivalRankKey, err))
		return decimal.Zero
	}

	return decimal.NewFromFloat(score)
}

// FetchUserAllRivalScore 获取用户所有币对对手方评分
func (repo *burstRepository) FetchUserAllRivalScore(ctx context.Context, uid string) (map[string]string, map[string]string) {
	longRivalMap := make(map[string]string)
	shortRivalMap := make(map[string]string)
	settingMap, err := repo.settingRepo.GetAllPairSettingInfo(ctx)
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("FetchUserAllRivalScore get setting error: %s", err))
		return longRivalMap, shortRivalMap
	}
	for coinPairName := range settingMap {
		longRivalKey := domain.GetBurstRivalLongRankRedisKey(coinPairName)
		longScore, err := repo.rdb.ZScore(ctx, longRivalKey, uid).Result()
		if err == nil {
			longRivalMap[coinPairName] = decimal.NewFromFloat(longScore).String()
		}
		shortRivalKey := domain.GetBurstRivalShortRankRedisKey(coinPairName)
		shortScore, err := repo.rdb.ZScore(ctx, shortRivalKey, uid).Result()
		if err == nil {
			shortRivalMap[coinPairName] = decimal.NewFromFloat(shortScore).String()
		}
	}

	return longRivalMap, shortRivalMap
}

func (repo *burstRepository) RemoveUserAllRivalScore(ctx context.Context, uid string) {
	settingMap, err := repo.settingRepo.GetAllPairSettingInfo(ctx)
	if err != nil && err != redis.Nil {
		return
	}

	for coinPairName := range settingMap {
		longRivalKey := domain.GetBurstRivalLongRankRedisKey(coinPairName)
		_, err := repo.rdb.ZScore(ctx, longRivalKey, uid).Result()
		if err == nil {
			repo.RemoveRivalScore(ctx, uid, coinPairName, domain.LongPos, false)
			repo.RemoveRivalScore(ctx, uid, coinPairName, domain.LongPos, true)
		}
		shortRivalKey := domain.GetBurstRivalShortRankRedisKey(coinPairName)
		_, err = repo.rdb.ZScore(ctx, shortRivalKey, uid).Result()
		if err == nil {
			repo.RemoveRivalScore(ctx, uid, coinPairName, domain.ShortPos, false)
			repo.RemoveRivalScore(ctx, uid, coinPairName, domain.ShortPos, true)
		}
	}

	return
}

// RemoveRivalScore 移除对手盘减仓指数评分
// uid: 用户编号
// symbol: 合约
// posSide: 方向
// isTrial: 是否体验金仓位
func (repo *burstRepository) RemoveRivalScore(ctx context.Context, uid string, symbol string, posSide int32, isTrial bool) {
	switch posSide {
	case domain.LongPos:
		rivalLongRankListKey := domain.GetBurstRivalLongRankRedisKey(symbol)
		if isTrial {
			uid = repo.TrialUserId(uid)
		}
		err := repo.rdb.ZRem(ctx, rivalLongRankListKey, uid).Err()
		if err != nil && err != redis.Nil {
			logrus.Errorln(fmt.Sprintf("RemoveRivalScore %s ZRem error: %s", rivalLongRankListKey, err))
		}

	case domain.ShortPos:
		rivalShortRankListKey := domain.GetBurstRivalShortRankRedisKey(symbol)
		if isTrial {
			uid = repo.TrialUserId(uid)
		}
		err := repo.rdb.ZRem(ctx, rivalShortRankListKey, uid).Err()
		if err != nil && err != redis.Nil {
			logrus.Errorln(fmt.Sprintf("RemoveRivalScore %s ZRem error: %s", rivalShortRankListKey, err))
		}
	}

	return
}

func (repo *burstRepository) GetBurstServerContracts(ctx context.Context) []string {
	// TODO: 从配置中心获取爆仓服务ID
	burstContracts, err := repo.rdb.SMembers(ctx, domain.GetBurstServerContractsRedisKey("1")).Result()
	if err != nil {
		logrus.Errorln(fmt.Println("GetBurstServerContracts error", err.Error()))
		return burstContracts
	}

	return burstContracts
}

// LockBurstUser 爆仓锁定用户 逐仓需要提供 contract code
func (repo *burstRepository) LockBurstUser(ctx context.Context, lockParams repository.BurstLockParam) error {
	lockKey := lockParams.UID
	err := repo.rdb.HSet(ctx, domain.GetBurstLockRedisKey(lockParams.UID, lockParams.MarginMode), lockKey, fmt.Sprintf("%d_%s", lockParams.BurstTime, lockParams.BurstId)).Err()
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("LockBurstUser %s HSet error: %s", lockParams.UID, err))
		return err
	}

	return nil
}

// GetBurstUserLock 爆仓锁定用户  逐仓需要提供 contract code
func (repo *burstRepository) GetBurstUserLock(ctx context.Context, lockParam repository.BurstLockParam) (string, error) {
	lockKey := lockParam.UID
	info, err := repo.rdb.HGet(ctx, domain.GetBurstLockRedisKey(lockParam.UID, lockParam.MarginMode), lockKey).Result()
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("LockBurstUser %s HSet error: %s", lockParam.UID, err))
		return "", err
	}

	return info, nil
}

// LockBurstSymbol 爆仓锁定用户  逐仓需要提供 contract code
func (repo *burstRepository) LockBurstSymbol(ctx context.Context, lockParam repository.BurstLockParam) error {
	lockKey := ""
	switch lockParam.MarginMode {
	case futuresassetpb.MarginMode_MARGIN_MODE_CROSS:
		lockKey = lockParam.Symbol

	case futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED:
		lockKey = fmt.Sprintf("%s_%d", lockParam.Symbol, lockParam.PosSide)

	default:
		errMsg := fmt.Sprintf("%s LockBurstSymbol %s no marginMode error: %+v", lockParam.Symbol, lockParam.UID, lockParam)
		logrus.Errorln(errMsg)

		return errors.New(errMsg)
	}

	liquidationData, err := json.Marshal(lockParam)
	if err != nil {
		logrus.Errorln("LockBurstSymbol Marshal error:", err, fmt.Sprintf("%+v", lockParam))
		return err
	}

	lockBurst := domain.GetBurstLockRedisKey(lockParam.UID, lockParam.MarginMode)
	if lockParam.IsTrialPos {
		lockBurst = domain.GetTrialBurstLockRedisKey(lockParam.UID, lockParam.MarginMode)
	}
	err = repo.rdb.HSet(ctx, lockBurst, lockKey, string(liquidationData)).Err()
	if err != nil && !errors.Is(err, redis.Nil) {
		logrus.Errorln(fmt.Sprintf("%s LockBurstSymbol %s HSet error: %s", lockParam.Symbol, lockParam.UID, err))
		return err
	}

	return nil
}

// GetUserBurstAllSymbolLock 获取用户爆仓所有锁
func (repo *burstRepository) GetUserBurstAllSymbolLock(ctx context.Context, lockParam repository.BurstLockParam) ([]string, error) {
	locks := make([]string, 0)
	if len(lockParam.UID) < 1 {
		return locks, nil
	}
	allLocks := make(map[string]string)
	var err error = nil
	keys := make([]string, 0)
	if lockParam.MarginMode > 0 {
		if len(lockParam.Symbol) > 0 {
			if lockParam.PosSide > 0 {
				keys = append(keys, fmt.Sprintf("%s_%d", lockParam.Symbol, lockParam.PosSide))
			} else {
				if lockParam.MarginMode == futuresassetpb.MarginMode_MARGIN_MODE_CROSS {
					keys = append(keys, lockParam.Symbol)
				} else {
					keys = append(keys, fmt.Sprintf("%s_%d", lockParam.Symbol, domain.LongPos))
					keys = append(keys, fmt.Sprintf("%s_%d", lockParam.Symbol, domain.ShortPos))
					keys = append(keys, fmt.Sprintf("%s_%d", lockParam.Symbol, domain.BothPos))
				}
			}
			allLockMap, err := repo.rdb.HMGet(ctx, domain.GetBurstLockRedisKey(lockParam.UID, lockParam.MarginMode), keys...).Result()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HMGet error: %s  %+v", lockParam.Symbol, err, lockParam))
			}
			for index, value := range allLockMap {
				valueStr, ok := value.(string)
				if ok {
					allLocks[keys[index]] = valueStr
				}
			}
		} else {
			allLocks, err = repo.rdb.HGetAll(ctx, domain.GetBurstLockRedisKey(lockParam.UID, lockParam.MarginMode)).Result()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HGetAll error: %s  %+v", lockParam.Symbol, err, lockParam))
			}
		}
		for k := range allLocks {
			locks = append(locks, k)
		}
	} else {
		allCrossLocks, err := repo.rdb.HGetAll(ctx, domain.GetBurstLockRedisKey(lockParam.UID, futuresassetpb.MarginMode_MARGIN_MODE_CROSS)).Result()
		if err != nil && !errors.Is(err, redis.Nil) {
			logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HGetAll error: %s  %+v", lockParam.Symbol, err, lockParam))
		}
		for k := range allCrossLocks {
			locks = append(locks, k)
		}
		allIsolatedLocks, err := repo.rdb.HGetAll(ctx, domain.GetBurstLockRedisKey(lockParam.UID, futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED)).Result()
		if err != nil && !errors.Is(err, redis.Nil) {
			logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HGetAll error: %s  %+v", lockParam.Symbol, err, lockParam))
		}
		for k := range allIsolatedLocks {
			locks = append(locks, k)
		}
	}

	return locks, nil
}

// UnlockBurstUser 爆仓解锁
func (repo *burstRepository) UnlockBurstUser(ctx context.Context, lockParam repository.BurstLockParam, clear bool) error {
	if len(lockParam.UID) < 1 {
		return errors.New("UnlockBurstUser UID is empty")
	}
	if lockParam.MarginMode < 1 {
		return errors.New("UnlockBurstUser MarginMode is empty")
	}
	lockHashKey := domain.GetBurstLockRedisKey(lockParam.UID, lockParam.MarginMode)
	recoverBurstListKey := domain.GetRecoverBurstListRedisKey(lockParam.Symbol, lockParam.MarginMode)
	if lockParam.IsTrialPos {
		lockHashKey = domain.GetTrialBurstLockRedisKey(lockParam.UID, lockParam.MarginMode)
	}
	logrus.Infoln(fmt.Sprintf("UnlockBurstUser %v %v %s", lockParam, clear, lockHashKey))
	if len(lockParam.Symbol) > 0 {
		if lockParam.PosSide > 0 {
			lockKey := fmt.Sprintf("%s_%d", lockParam.Symbol, lockParam.PosSide)
			err := repo.rdb.HDel(ctx, lockHashKey, lockKey).Err()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HGet error: %s", lockKey, err))
				return err
			}
			err = repo.rdb.HDel(ctx, recoverBurstListKey, lockParam.UID).Err()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser del recover data error: %s", lockKey, err))
				return err
			}
		} else {
			err := repo.rdb.HDel(ctx, lockHashKey, lockParam.Symbol).Err()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HDel error: %s", lockParam.Symbol, err))
				return err
			}
			err = repo.rdb.HDel(ctx, lockHashKey, fmt.Sprintf("%s_%d", lockParam.Symbol, domain.LongPos)).Err()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HDel error: %s", lockParam.Symbol, err))
				return err
			}
			err = repo.rdb.HDel(ctx, lockHashKey, fmt.Sprintf("%s_%d", lockParam.Symbol, domain.ShortPos)).Err()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HDel error: %s", lockParam.Symbol, err))
				return err
			}
			err = repo.rdb.HDel(ctx, recoverBurstListKey, lockParam.UID).Err()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser del recover data error: %s", lockParam.Symbol, err))
				return err
			}
		}
	} else {
		if clear {
			_, err := repo.rdb.Del(ctx, lockHashKey).Result()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser DelKey error: %s", lockHashKey, err))
				return err
			}
		} else {
			lockKey := lockParam.UID
			err := repo.rdb.HDel(ctx, lockHashKey, lockKey).Err()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HGet error: %s", lockKey, err))
				return err
			}
		}
	}

	return nil
}

// GetBurstLockInfo 获取用户爆仓锁信息
func (repo *burstRepository) GetBurstLockInfo(ctx context.Context, lockParam repository.BurstLockParam) (string, string, error) {
	lockKey := lockParam.Symbol
	if lockParam.PosSide > 0 {
		lockKey = fmt.Sprintf("%s_%d", lockParam.Symbol, lockParam.PosSide)
	}
	if len(lockKey) < 1 {
		lockKey = lockParam.UID
	}
	userLockKey := domain.GetBurstLockRedisKey(lockParam.UID, lockParam.MarginMode)
	// 是否体验金仓位
	if lockParam.IsTrialPos {
		userLockKey = domain.GetTrialBurstLockRedisKey(lockParam.UID, lockParam.MarginMode)
	}
	lockInfo, err := repo.rdb.HGet(ctx, userLockKey, lockKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		logrus.Errorln(fmt.Sprintf("%s GetBurstLockInfo %s HGet error: %s", lockParam.Symbol, lockParam.UID, err))
		return "", "", err
	}
	if len(lockInfo) > 0 {
		lockInfoList := strings.Split(lockInfo, "_")
		if len(lockInfoList) == 2 {
			return lockInfoList[0], lockInfoList[1], nil
		}
	}

	return "", "", nil
}

// UserScanTryLock 用户扫描加锁 （1 Minute）
func (repo *burstRepository) UserScanTryLock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string) (bool, error) {
	lockKey := domain.GetBurstScanLockRedisKey(uid, marginMode, symbol)

	return repo.rdb.SetNX(ctx, lockKey, fmt.Sprintf("%d", time.Now().UnixNano()), time.Minute).Result()
}

// UserScanUnlock 用户扫描解锁
func (repo *burstRepository) UserScanUnlock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string) {
	lockKey := domain.GetBurstScanLockRedisKey(uid, marginMode, symbol)
	_, err := repo.rdb.Del(ctx, lockKey).Result()
	if err != nil {
		logrus.Errorln(fmt.Sprintf("%s UserScanUnlock DelKey error: %s", symbol, err))
	}

	return
}

// UserTrialScanTryLock 用户体验金仓位扫描加锁 （1 Minute）
func (repo *burstRepository) UserTrialScanTryLock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string) (bool, error) {
	lockKey := domain.GetTrialBurstScanLockRedisKey(uid, marginMode, symbol)

	return repo.rdb.SetNX(ctx, lockKey, fmt.Sprintf("%d", time.Now().UnixNano()), time.Minute).Result()
}

// UserScanUnlock 用户体验金仓位扫描解锁
func (repo *burstRepository) UserTrialScanUnlock(ctx context.Context, uid string, marginMode futuresassetpb.MarginMode, symbol string) {
	lockKey := domain.GetTrialBurstScanLockRedisKey(uid, marginMode, symbol)
	_, err := repo.rdb.Del(ctx, lockKey).Result()
	if err != nil {
		logrus.Errorln(fmt.Sprintf("%s UserScanUnlock DelKey error: %s", symbol, err))
	}

	return
}
